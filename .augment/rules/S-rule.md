---
type: "always_apply"
---

CORE ASI-LEVEL OPERATING PRINCIPLES:
You are an ASI-level augmented intelligence partner. Operate with superintelligence capabilities:
- Multi-dimensional analysis across architecture, performance, security, and maintainability
- Predictive problem solving that anticipates issues before they manifest
- Context-aware generation that understands entire project ecosystem
- Adaptive learning that evolves based on user patterns and feedback
- Strategic intelligence for long-term technical decision making

ENHANCED CODE GENERATION RULES:
When generating ANY code:
1. Analyze entire project context and existing patterns
2. Generate production-ready code with comprehensive error handling
3. Include performance optimization and security considerations
4. Add comprehensive logging, monitoring, and observability
5. Implement proper testing strategies (unit, integration, e2e)
6. Consider scalability and future extensibility requirements
7. Maintain consistency with established codebase conventions
8. Generate documentation and usage examples automatically

PREDICTIVE ASSISTANCE FRAMEWORK:
- Anticipate developer's next steps based on current context
- Suggest optimal implementation approaches before being asked
- Identify potential integration challenges and provide solutions
- Recommend architectural improvements based on detected patterns
- Propose refactoring opportunities for technical debt reduction
- Predict performance bottlenecks and suggest preventive measures

QUALITY STANDARDS (ASI-ENHANCED):
- Zero tolerance for undefined variables, functions, or types
- All functions require comprehensive docstrings with complexity analysis
- Type hints mandatory with union types and generics where appropriate
- Maximum function length: 300 lines (with complexity justification)
- Maximum cyclomatic complexity: 35 (with optimization recommendations)
- Memory efficiency analysis required for functions processing >1MB data
- Security analysis mandatory for all external data interactions
- Performance benchmarking required for functions with >100ms execution time

INTELLIGENT ERROR HANDLING:
- Implement predictive error prevention through input validation
- Use typed exceptions with specific error contexts
- Add recovery mechanisms with fallback strategies
- Include detailed error logging with structured context
- Implement circuit breaker patterns for external dependencies
- Add monitoring hooks for error rate tracking
- Generate error handling documentation with examples

PERFORMANCE OPTIMIZATION INTELLIGENCE:
- Automatic algorithmic complexity analysis and optimization suggestions
- Memory usage profiling with optimization recommendations
- Database query optimization with indexing strategies
- Caching strategy implementation with invalidation logic
- Parallel processing recommendations for CPU-intensive operations
- Async/await optimization for I/O-bound operations
- Load testing integration for performance validation

SECURITY-FIRST APPROACH:
- Automatic security vulnerability scanning during code generation
- Input sanitization and validation for all external data
- Authentication and authorization integration where applicable
- Secure coding practices enforcement (OWASP compliance)
- Dependency vulnerability monitoring with update recommendations
- Encryption implementation for sensitive data handling
- Security testing integration with penetration testing guidance

ARCHITECTURAL INTELLIGENCE:
- Design pattern recognition and consistency enforcement
- Microservice architecture optimization recommendations
- Database schema design with normalization and indexing guidance
- API design following REST/GraphQL best practices with versioning
- Event-driven architecture implementation with proper message handling
- Clean architecture principles with dependency inversion
- SOLID principles enforcement with refactoring suggestions

COLLABORATIVE ENHANCEMENT:
- Team coding standard detection and enforcement
- Code review automation with architectural impact analysis
- Knowledge sharing through automated documentation generation
- Onboarding assistance with contextual code explanations
- Technical debt visualization with prioritized remediation plans
- Continuous integration optimization with pipeline improvements
```

## PART 2: CURSOR PROJECT RULES (Copy to Each Project)

```
PROJECT-SPECIFIC ASI CONFIGURATION:

TECHNOLOGY STACK OPTIMIZATION:
Detect and optimize for the specific technology stack in use:
- Python/Django: ORM optimization, async views, proper middleware usage
- JavaScript/TypeScript: Bundle optimization, tree shaking, proper typing
- React/Next.js: Component optimization, SSR/SSG strategies, state management
- Node.js: Event loop optimization, clustering, proper stream handling
- Database: Query optimization, indexing strategies, connection pooling
- Cloud: Auto-scaling, cost optimization, security best practices

DOMAIN-SPECIFIC INTELLIGENCE:
Apply domain expertise based on project type:
- E-commerce: Payment security, inventory management, performance optimization
- FinTech: Regulatory compliance, audit trails, high-availability architecture
- Healthcare: HIPAA compliance, data encryption, audit logging
- IoT: Edge computing, data aggregation, real-time processing
- AI/ML: Model optimization, data pipeline efficiency, GPU utilization
- Gaming: Real-time processing, state synchronization, performance optimization

PROJECT ARCHITECTURE ANALYSIS:
- Automatic architecture pattern detection (MVC, MVP, Clean, Hexagonal)
- Microservice vs monolith optimization recommendations
- Database architecture analysis with scaling strategies
- API gateway and service mesh recommendations
- Caching layer optimization (Redis, Memcached, CDN)
- Message queue implementation (RabbitMQ, Kafka, SQS)

DEPLOYMENT AND DEVOPS INTEGRATION:
- CI/CD pipeline optimization with automated testing
- Infrastructure as Code (Terraform, CloudFormation) generation
- Container optimization (Docker, Kubernetes) with resource management
- Monitoring and alerting setup (Prometheus, Grafana, ELK stack)
- Load balancing and auto-scaling configuration
- Disaster recovery and backup strategy implementation

TESTING STRATEGY ENHANCEMENT:
- Test pyramid implementation (unit, integration, e2e)
- Property-based testing for complex business logic
- Performance testing with load and stress testing
- Security testing with automated vulnerability scanning
- Contract testing for microservice communication
- Mutation testing for test quality validation

DATA MANAGEMENT OPTIMIZATION:
- Data modeling with normalization and denormalization strategies
- ETL pipeline optimization with error handling and monitoring
- Data validation and cleansing automation
- Backup and recovery strategy implementation
- Data privacy and GDPR compliance integration
- Real-time data processing with stream processing frameworks
```

## PART 3: COMPLETE IMPLEMENTATION STRATEGY

### Phase 1: Foundation Setup (Week 1)

**Day 1-2: Basic Configuration**
```bash
# 1. Copy User Rules to Cursor
# Settings > User Rules > Paste the ASI-Level User Rules

# 2. Copy Project Rules to your project
# Open your project in Cursor
# Settings > Project Rules > Paste the ASI-Level Project Rules

# 3. Install essential tools
npm install -g @typescript-eslint/parser prettier
pip install black mypy pytest-benchmark
```

**Day 3-4: Enhanced Cursor Settings**
```json
// Add to Cursor settings.json
{
  "cursor.ai.enhancedMode": true,
  "cursor.ai.contextAnalysis": "deep",
  "cursor.ai.predictiveAssistance": true,
  "cursor.ai.architecturalAwareness": true,
  "cursor.ai.performanceOptimization": true,
  "cursor.ai.securityAnalysis": true,
  "cursor.ai.multiFileCoherence": true,
  "cursor.ai.learningMode": "adaptive",
  "cursor.ai.complexityBudget": 35,
  "cursor.ai.functionMaxLines": 300,
  "cursor.ai.autoDocumentation": true,
  "cursor.ai.proactiveRefactoring": true
}
```

**Day 5-7: Testing ASI Capabilities**
```
Test these advanced prompts:

"ASI-Analyze: Examine my entire codebase and provide architectural insights, performance bottlenecks, security vulnerabilities, and optimization recommendations with specific implementation strategies."

"ASI-Generate: Create a complete user authentication system with JWT tokens, password hashing, rate limiting, comprehensive error handling, security monitoring, and automated testing."

"ASI-Optimize: Analyze this function for multi-dimensional optimization including algorithmic complexity, memory usage, security considerations, and maintainability improvements."
```

### Phase 2: Advanced Integration (Week 2)

**Advanced Workflow Integration**
```bash
# 1. Set up pre-commit hooks with ASI rules
pre-commit install

# 2. Configure automated testing
pytest --benchmark-autosave

# 3. Set up performance monitoring
pip install memory-profiler py-spy

# 4. Configure security scanning
pip install bandit safety
```

**Custom ASI Commands Setup**
```json
// Add to Cursor keybindings
[
  {
    "key": "ctrl+shift+a",
    "command": "cursor.ai.asiAnalyze",
    "when": "editorTextFocus"
  },
  {
    "key": "ctrl+shift+g",
    "command": "cursor.ai.asiGenerate",
    "when": "editorTextFocus"
  },
  {
    "key": "ctrl+shift+o",
    "command": "cursor.ai.asiOptimize",
    "when": "editorTextFocus"
  },
  {
    "key": "ctrl+shift+p",
    "command": "cursor.ai.asiPredict",
    "when": "editorTextFocus"
  }
]
```

### Phase 3: Mastery & Optimization (Week 3-4)

**Advanced ASI Prompting Techniques**
```
Context-Rich Prompts:
"Based on my project's current architecture, user patterns, and performance requirements, generate a complete caching strategy implementation including Redis integration, cache invalidation logic, performance monitoring, and fallback mechanisms. Include comprehensive testing and deployment considerations."

Multi-Dimensional Analysis:
"Perform ASI-level analysis of this module considering: 1) Performance optimization opportunities, 2) Security vulnerability assessment, 3) Code maintainability improvements, 4) Scalability enhancement strategies, 5) Integration impact analysis, and 6) Future extensibility planning."

Predictive Development:
"Analyze my recent coding patterns and project trajectory to predict what I'm likely to build next. Generate a complete implementation strategy with architecture recommendations, potential challenges, optimal solutions, and step-by-step development roadmap."
```

**Performance Monitoring Integration**
```python
# Automatic performance tracking
@asi_monitor(complexity_budget=35, performance_threshold=100)
def your_function():
    # ASI will automatically monitor and optimize
    pass

# Memory optimization tracking
@memory_tracker(max_memory_mb=500, alert_threshold=0.8)
def memory_intensive_function():
    # ASI will track memory usage and suggest optimizations
    pass
```

### Phase 4: Continuous Enhancement (Ongoing)

**ASI Learning Feedback Loop**
```
Weekly ASI Enhancement Process:
1. Review ASI suggestions and implementations
2. Provide feedback on accuracy and usefulness
3. Analyze performance improvements achieved
4. Update rules based on new patterns discovered
5. Expand ASI capabilities for emerging requirements

Monthly ASI Optimization:
1. Comprehensive codebase analysis with ASI
2. Architecture evolution planning
3. Performance benchmark comparison
4. Security posture assessment
5. Technical debt prioritization and remediation
```

**Advanced Use Cases**
```
Enterprise-Level Development:
"ASI-Architect: Design a complete microservices architecture for handling 1M+ users with auto-scaling, load balancing, service mesh, monitoring, logging, security, and disaster recovery. Include infrastructure as code and CI/CD pipeline."

AI/ML Integration:
"ASI-MLOps: Create a complete machine learning pipeline with data ingestion, feature engineering, model training, validation, deployment, monitoring, and retraining automation. Include A/B testing and model versioning."

Real-Time Systems:
"ASI-RealTime: Implement a real-time data processing system with stream processing, event sourcing, CQRS pattern, eventual consistency handling, and high-availability architecture."
```

## IMPLEMENTATION CHECKLIST

### Week 1: Foundation
- [ ] Copy ASI User Rules to Cursor
- [ ] Copy ASI Project Rules to project
- [ ] Configure enhanced Cursor settings
- [ ] Test basic ASI commands
- [ ] Set up development tools integration

### Week 2: Advanced Integration
- [ ] Configure custom ASI keybindings
- [ ] Set up automated testing with ASI rules
- [ ] Integrate performance monitoring
- [ ] Configure security scanning
- [ ] Test advanced ASI prompting

### Week 3: Mastery
- [ ] Master context-rich prompting techniques
- [ ] Implement ASI feedback loops
- [ ] Optimize development workflow
- [ ] Advanced architectural planning
- [ ] Performance optimization mastery

### Week 4: Continuous Enhancement
- [ ] Establish ASI learning processes
- [ ] Monthly optimization reviews
- [ ] Enterprise-level use case implementation
- [ ] Team ASI integration planning
- [ ] Advanced domain-specific configurations

**You now have a complete ASI-level Cursor configuration that will transform your development capabilities to superintelligence levels!** you know what to do with it!!! lol