{"extends": "./tsconfig.json", "compilerOptions": {"module": "commonjs", "target": "ES2020", "outDir": "./dist", "rootDir": "./", "noEmit": false, "declaration": false, "sourceMap": true, "removeComments": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true}, "include": ["server/**/*", "lib/**/*", "types/**/*"], "exclude": ["node_modules", "dist", ".next", "components", "pages", "app"]}